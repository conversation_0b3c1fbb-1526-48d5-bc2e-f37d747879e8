# Signora - Learning App

A beautiful Flutter learning app with a modern UI design featuring live sessions, course management, and progress tracking.

## Features

### 🏠 Home Screen
- **Search Functionality**: Quick search bar for finding courses and content
- **Welcome Section**: Personalized greeting with user profile access
- **Live Session Card**: Prominent display of current live sessions with instructor details
- **Category Tabs**: Easy navigation between different course categories (Graphic Design, User Interface, UI UX)
- **Course Sections**:
  - Ongoing Courses with progress tracking
  - Suggested Courses with pricing information

### 🎨 Design Elements
- **Modern UI**: Clean, professional design with blue gradient themes
- **Responsive Layout**: Optimized for mobile devices
- **Interactive Components**: Smooth animations and touch feedback
- **Progress Indicators**: Visual progress bars for ongoing courses
- **Bottom Navigation**: Easy access to Home, Schedule, Tasks, and Profile

### 📱 Navigation
- **Bottom Navigation Bar**: Four main sections
  - Home (current screen)
  - Schedule
  - Tasks
  - Profile

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── screens/
│   └── home_screen.dart     # Main home screen
├── widgets/
│   ├── search_bar_widget.dart
│   ├── welcome_section.dart
│   ├── live_session_card.dart
│   ├── category_tabs.dart
│   ├── course_card.dart
│   └── bottom_navigation.dart
├── models/
│   └── course.dart          # Data models
└── utils/
    └── colors.dart          # App color constants
```

## Getting Started

### Prerequisites
- Flutter SDK (latest stable version)
- Dart SDK
- Android Studio / VS Code
- Android device or emulator

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd signora
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the app:
```bash
flutter run
```

### Running Tests

```bash
flutter test
```

## Sample Data

The app currently uses sample data including:
- Live session with Dr. Ahmed Amadi (Data Analytics)
- Ongoing courses with progress tracking
- Suggested courses with pricing
- User profile for "Muhamed Shaban"

## Customization

### Colors
Modify `lib/utils/colors.dart` to change the app's color scheme.

### Sample Data
Update the data in `lib/screens/home_screen.dart` to use your own course and user data.

### Styling
Each widget has its own styling that can be customized individually.

## Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
