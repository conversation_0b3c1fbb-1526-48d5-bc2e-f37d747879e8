class Course {
  final String id;
  final String title;
  final String instructor;
  final String category;
  final double progress;
  final String duration;
  final double price;
  final String imageUrl;
  final bool isLive;
  final DateTime? liveTime;

  Course({
    required this.id,
    required this.title,
    required this.instructor,
    required this.category,
    this.progress = 0.0,
    required this.duration,
    this.price = 0.0,
    this.imageUrl = '',
    this.isLive = false,
    this.liveTime,
  });
}

class LiveSession {
  final String id;
  final String title;
  final String instructor;
  final String category;
  final DateTime startTime;
  final DateTime endTime;
  final String imageUrl;
  final bool isLive;

  LiveSession({
    required this.id,
    required this.title,
    required this.instructor,
    required this.category,
    required this.startTime,
    required this.endTime,
    this.imageUrl = '',
    this.isLive = true,
  });

  String get formattedDate {
    final months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    return '${startTime.day} ${months[startTime.month - 1]}, ${startTime.year}';
  }

  String get formattedTime {
    String formatTime(DateTime time) {
      final hour = time.hour;
      final minute = time.minute.toString().padLeft(2, '0');
      final period = hour >= 12 ? 'pm' : 'am';
      final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
      return '$displayHour:$minute $period';
    }

    return '${formatTime(startTime)} - ${formatTime(endTime)}';
  }
}
