import 'package:flutter/material.dart';
import '../models/course.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/welcome_section.dart';
import '../widgets/live_session_card.dart';
import '../widgets/category_tabs.dart';
import '../widgets/course_card.dart';
import '../widgets/bottom_navigation.dart';
import '../utils/colors.dart';
import 'profile_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  String _selectedCategory = 'UI UX';

  final List<String> _categories = [
    'Graphic Design',
    'User Interface',
    'UI UX',
    'User Interface',
  ];

  // Sample data
  late final LiveSession _liveSession;
  late final List<Course> _ongoingCourses;
  late final List<Course> _suggestedCourses;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    _liveSession = LiveSession(
      id: '1',
      title: 'Live Session',
      instructor: 'Dr. <PERSON> Amadi',
      category: 'Data Analytics',
      startTime: DateTime(2025, 6, 24, 8, 0),
      endTime: DateTime(2025, 6, 24, 9, 0),
      isLive: true,
    );

    _ongoingCourses = [
      Course(
        id: '1',
        title: 'Basics of UI/UX Design',
        instructor: 'Dr. Ahmed Hassan',
        category: 'UI UX',
        progress: 0.6,
        duration: '2 hours',
      ),
      Course(
        id: '2',
        title: 'Basics of UI/UX Design',
        instructor: 'Dr. Ahmed Hassan',
        category: 'UI UX',
        progress: 0.4,
        duration: '3 hours',
      ),
    ];

    _suggestedCourses = [
      Course(
        id: '3',
        title: 'Basics of UI/UX Design Workflow',
        instructor: 'Ahmed hassan',
        category: 'UI UX',
        price: 55,
        duration: '2 hours',
      ),
      Course(
        id: '4',
        title: 'Basics of UI/UX Design Workflow',
        instructor: 'Ahmed hassan',
        category: 'UI UX',
        price: 55,
        duration: '2 hours',
      ),
      Course(
        id: '5',
        title: 'Basics of UI/UX Design',
        instructor: 'Ahmed hassan',
        category: 'UI UX',
        price: 45,
        duration: '1.5 hours',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 10),

              // Search Bar
              const SearchBarWidget(),

              const SizedBox(height: 20),

              // Welcome Section
              const WelcomeSection(userName: 'Muhamed Shaban'),

              const SizedBox(height: 20),

              // Live Session Card
              LiveSessionCard(session: _liveSession),

              // Category Tabs
              CategoryTabs(
                categories: _categories,
                selectedCategory: _selectedCategory,
                onCategorySelected: (category) {
                  setState(() {
                    _selectedCategory = category;
                  });
                },
              ),

              // Ongoing Courses Section
              _buildSectionHeader('Ongoing Courses'),
              const SizedBox(height: 16),
              _buildOngoingCourses(),

              const SizedBox(height: 32),

              // Suggested Courses Section
              _buildSectionHeader('Suggested Courses'),
              const SizedBox(height: 16),
              _buildSuggestedCourses(),

              const SizedBox(height: 100), // Bottom padding for navigation
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: _currentIndex,
        onTap: (index) {
          if (index == 3) {
            // Navigate to profile screen
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const ProfileScreen()),
            );
          } else {
            setState(() {
              _currentIndex = index;
            });
          }
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: AppColors.primaryText,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Text(
            'View All',
            style: TextStyle(
              color: AppColors.primaryBlue,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOngoingCourses() {
    return SizedBox(
      height: 240,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: _ongoingCourses.length,
        itemBuilder: (context, index) {
          return CourseCard(course: _ongoingCourses[index], showProgress: true);
        },
      ),
    );
  }

  Widget _buildSuggestedCourses() {
    return SizedBox(
      height: 240,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: _suggestedCourses.length,
        itemBuilder: (context, index) {
          return CourseCard(course: _suggestedCourses[index], showPrice: true);
        },
      ),
    );
  }
}
