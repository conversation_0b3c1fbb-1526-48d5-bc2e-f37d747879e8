import 'package:flutter/material.dart';
import '../utils/colors.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/social_login_button.dart';
import 'login_screen.dart';
import 'create_account_screen.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 60),

              // Title
              const Center(
                child: Text(
                  'Register',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.w700,
                    color: AppColors.primaryText,
                  ),
                ),
              ),

              const SizedBox(height: 60),

              // Email Field
              const Text(
                'Email or Phone\'s number',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primaryText,
                ),
              ),
              const SizedBox(height: 12),
              AuthTextField(
                controller: _emailController,
                hintText: '<EMAIL>',
                suffixIcon: Icons.alternate_email,
              ),

              const SizedBox(height: 24),

              // Password Field
              const Text(
                'Your password',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primaryText,
                ),
              ),
              const SizedBox(height: 12),
              AuthTextField(
                controller: _passwordController,
                hintText: 'Input your password',
                obscureText: _obscurePassword,
                suffixIcon: _obscurePassword
                    ? Icons.visibility_off
                    : Icons.visibility,
                onSuffixTap: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),

              const SizedBox(height: 24),

              // Confirm Password Field
              const Text(
                'Re-Enter Password',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primaryText,
                ),
              ),
              const SizedBox(height: 12),
              AuthTextField(
                controller: _confirmPasswordController,
                hintText: 'Input your password',
                obscureText: _obscureConfirmPassword,
                suffixIcon: _obscureConfirmPassword
                    ? Icons.visibility_off
                    : Icons.visibility,
                onSuffixTap: () {
                  setState(() {
                    _obscureConfirmPassword = !_obscureConfirmPassword;
                  });
                },
              ),

              const SizedBox(height: 40),

              // Register Button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CreateAccountScreen(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Register',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.whiteText,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Login Link
              Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Already have an account? ',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.secondaryText,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LoginScreen(),
                          ),
                        );
                      },
                      child: const Text(
                        'Log In',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Social Login Buttons
              const SocialLoginButton(
                icon: Icons.apple,
                text: 'Continue With Apple ID',
                backgroundColor: AppColors.cardBackground,
                textColor: AppColors.primaryText,
              ),

              const SizedBox(height: 16),

              const SocialLoginButton(
                icon: Icons.facebook,
                text: 'Continue With Facebook',
                backgroundColor: AppColors.cardBackground,
                textColor: AppColors.primaryText,
                iconColor: Colors.blue,
              ),

              const SizedBox(height: 16),

              const SocialLoginButton(
                icon: Icons.g_mobiledata,
                text: 'Continue With Google',
                backgroundColor: AppColors.cardBackground,
                textColor: AppColors.primaryText,
                iconColor: Colors.red,
              ),

              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
