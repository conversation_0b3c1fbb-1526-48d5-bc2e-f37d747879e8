import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:signora/main.dart';

void main() {
  testWidgets('Home screen loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that key elements are present
    expect(find.text('Welcome Back!'), findsOneWidget);
    expect(find.text('Muhamed Shaban'), findsOneWidget);
    expect(find.text('Dr. <PERSON>'), findsOneWidget);
    expect(find.text('Live Now'), findsOneWidget);
    expect(find.text('Data Analytics'), findsOneWidget);
    expect(find.text('Ongoing Courses'), findsOneWidget);
    expect(find.text('Suggested Courses'), findsOneWidget);

    // Verify search bar is present
    expect(find.byType(TextField), findsOneWidget);

    // Verify bottom navigation is present
    expect(find.text('Home'), findsOneWidget);
    expect(find.text('Schedule'), findsOneWidget);
    expect(find.text('Tasks'), findsOneWidget);
    expect(find.text('Profile'), findsOneWidget);
  });

  testWidgets('Category tabs work correctly', (WidgetTester tester) async {
    await tester.pumpWidget(const MyApp());

    // Find and tap on a different category
    expect(find.text('UI UX'), findsOneWidget);
    expect(find.text('Graphic Design'), findsOneWidget);

    await tester.tap(find.text('Graphic Design'));
    await tester.pump();

    // The app should still work after category change
    expect(find.text('Ongoing Courses'), findsOneWidget);
  });
}
