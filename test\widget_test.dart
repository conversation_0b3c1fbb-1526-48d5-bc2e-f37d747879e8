import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:signora/main.dart';

void main() {
  testWidgets('Login screen loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that login screen elements are present
    expect(find.text('Email or Phone\'s number'), findsOneWidget);
    expect(find.text('Your password'), findsOneWidget);
    expect(find.text('Save me'), findsOneWidget);
    expect(find.text('Forgot your password?'), findsOneWidget);
    expect(find.text('Don\'t have an account? '), findsOneWidget);
    expect(find.text('Continue With Apple ID'), findsOneWidget);
    expect(find.text('Continue With Facebook'), findsOneWidget);
    expect(find.text('Continue With Google'), findsOneWidget);

    // Verify text fields are present
    expect(
      find.byType(TextField),
      findsNWidgets(2),
    ); // Email and password fields

    // Verify login button is present
    expect(find.widgetWithText(ElevatedButton, 'Log In'), findsOneWidget);
  });

  testWidgets('Navigation to register screen works', (
    WidgetTester tester,
  ) async {
    await tester.pumpWidget(const MyApp());

    // Tap on Register link (not the button)
    final registerLinks = find.text('Register');
    await tester.tap(registerLinks.first);
    await tester.pumpAndSettle();

    // Verify register screen is shown
    expect(find.text('Re-Enter Password'), findsOneWidget);
    expect(find.text('Already have an account? '), findsOneWidget);
    expect(find.widgetWithText(ElevatedButton, 'Register'), findsOneWidget);
  });

  testWidgets('Login button navigates to home screen', (
    WidgetTester tester,
  ) async {
    await tester.pumpWidget(const MyApp());

    // Tap on Log In button
    await tester.tap(find.widgetWithText(ElevatedButton, 'Log In'));
    await tester.pumpAndSettle();

    // Verify home screen is shown
    expect(find.text('Welcome Back!'), findsOneWidget);
    expect(find.text('Muhamed Shaban'), findsOneWidget);
    expect(find.text('Dr. Ahmed Amadi'), findsOneWidget);
  });
}
